package com.tool.pams.repository.domain.cmdb.db;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用日志清理记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_pams_log_clean_record")
@Schema(name = "PamsLogCleanRecordDO对象", description = "应用日志清理记录表")
public class PamsLogCleanRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "`id`", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "应用ID")
    @TableField("`app_id`")
    private Long appId;

    @Schema(description = "服务器IP")
    @TableField("`server_ip`")
    private String serverIp;

    @Schema(description = "清理文件路径")
    @TableField("`clean_file_path`")
    private String cleanFilePath;

    @Schema(description = "清理文件名称")
    @TableField("`clean_file_name`")
    private String cleanFileName;

    @Schema(description = "清理时间")
    @TableField("`clean_time`")
    private LocalDateTime cleanTime;

    @Schema(description = "操作类型(MANUAL=手动清理,SCHEDULED=定时任务)")
    @TableField("`operation_type`")
    private String operationType;

    @Schema(description = "清理原因(THRESHOLD=文件达到阈值,SCHEDULED_TIME=达到固定清理时间,MANUAL=手动清理)")
    @TableField("`clean_reason`")
    private String cleanReason;

    @Schema(description = "操作结果(SUCCESS=成功,FAILED=失败)")
    @TableField("`operation_result`")
    private String operationResult;

    @Schema(description = "失败原因")
    @TableField("`failure_reason`")
    private String failureReason;

    @Schema(description = "清理前大小(字节)")
    @TableField("`size_before_clean`")
    private Long sizeBeforeClean;

    @Schema(description = "清理后大小(字节)")
    @TableField("`size_after_clean`")
    private Long sizeAfterClean;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;
}
