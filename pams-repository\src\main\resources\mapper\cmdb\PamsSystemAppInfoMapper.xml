<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.PamsSystemAppInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO">
        <id column="id" property="id" />
        <result column="parent_app_id" property="parentAppId" />
        <result column="app_no" property="appNo" />
        <result column="app_type" property="appType" />
        <result column="develop_language" property="developLanguage" />
        <result column="account_id" property="accountId" />
        <result column="app_name" property="appName" />
        <result column="app_code" property="appCode" />
        <result column="develop_director" property="developDirector" />
        <result column="gitlab_address" property="gitlabAddress" />
        <result column="jenkins_address" property="jenkinsAddress" />
        <result column="deploy_port" property="deployPort" />
        <result column="deploy_path" property="deployPath" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
        <result column="remark" property="remark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, parent_app_id, app_no, app_type, develop_language, account_id, app_name, app_code, develop_director, gitlab_address, jenkins_address, deploy_port, deploy_path, create_time, update_time, creator, updater, deleted, remark
    </sql>

    <select id="syncSystemAppInfo" resultType="com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO">
        SELECT
            app.id,
            app.create_time,
            app.update_time,
            app.is_delete as deleted,
            app.version,
            app.parent_app_id,
            app.app_no,
            app.app_type,
            app.develop_language,
            app.account_id,
            app.app_name,
            app.app_code,
            app.develop_director,
            app.gitlab_address,
            app.jenkins_address,
            app.deploy_port,
            app.deploy_path,
            app.remark
        FROM
            t_cmdb_system_app_info app
        ORDER BY
            create_time
    </select>
    
    <insert id="insertOrUpdateBatch">
        INSERT INTO t_pams_system_app_info (
            id, parent_app_id, app_no, app_type, develop_language, account_id, app_name, app_code,
            develop_director, gitlab_address, jenkins_address, deploy_port, deploy_path, create_time,
            update_time, creator, updater, deleted, remark
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.parentAppId}, #{record.appNo}, #{record.appType}, #{record.developLanguage},
                #{record.accountId}, #{record.appName}, #{record.appCode}, #{record.developDirector},
                #{record.gitlabAddress}, #{record.jenkinsAddress}, #{record.deployPort}, #{record.deployPath},
                #{record.createTime}, #{record.updateTime}, #{record.creator}, #{record.updater}, #{record.deleted},
                #{record.remark}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            parent_app_id = VALUES(parent_app_id),
            app_no = VALUES(app_no),
            app_type = VALUES(app_type),
            develop_language = VALUES(develop_language),
            account_id = VALUES(account_id),
            app_name = VALUES(app_name),
            app_code = VALUES(app_code),
            develop_director = VALUES(develop_director),
            gitlab_address = VALUES(gitlab_address),
            jenkins_address = VALUES(jenkins_address),
            deploy_port = VALUES(deploy_port),
            deploy_path = VALUES(deploy_path),
            update_time = VALUES(update_time),
            updater = VALUES(updater),
            deleted = VALUES(deleted),
            remark = VALUES(remark)
    </insert>

    <!-- 分页查询模块信息 -->
    <select id="pageModuleInfo" resultType="com.tool.pams.repository.domain.cmdb.vo.ModuleInfoVO">
        SELECT
            app.id as moduleId,
            app.app_code as appCode,
            app.app_name as appName,
            NULL as apiCount,
            app.deploy_port as deployPort,
            app.deploy_path as deployPath,
            GROUP_CONCAT(DISTINCT msr.server_name) as serverNames,
            app.update_time as updateTime
        FROM t_pams_system_app_info app
        LEFT JOIN t_module_server_relation msr ON app.id = msr.sub_app_id AND msr.deleted = 0
        WHERE app.deleted = 0
        AND app.parent_app_id != 0
        <if test="params.appCode != null and params.appCode != ''">
            AND app.app_code LIKE CONCAT('%', #{params.appCode}, '%')
        </if>
        <if test="params.appName != null and params.appName != ''">
            AND app.app_name = #{params.appName}
        </if>
        GROUP BY app.id
        ORDER BY app.update_time DESC
    </select>

</mapper>
