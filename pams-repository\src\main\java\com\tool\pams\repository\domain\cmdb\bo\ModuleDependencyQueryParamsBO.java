package com.tool.pams.repository.domain.cmdb.bo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO;
import com.tool.pams.repository.domain.common.PageParamsBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <p>
 * 模块依赖表查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ModuleDependencyQueryParamsBO对象", description = "模块依赖表查询参数")
public class ModuleDependencyQueryParamsBO extends PageParamsBO<ModuleDependencyDO> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "应用id")
    private Long appId;

    @Schema(description = "groupId")
    private String groupId;

    @Schema(description = "artifactId")
    private String artifactId;

    @Schema(description = "版本")
    private String version;

    @Override
    public LambdaQueryWrapper<ModuleDependencyDO> buildQueryWrapper() {
        LambdaQueryWrapper<ModuleDependencyDO> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(id != null, ModuleDependencyDO::getId, id)
                .eq(appId != null, ModuleDependencyDO::getAppId, appId)
                .like(StringUtils.isNotBlank(groupId), ModuleDependencyDO::getGroupId, groupId)
                .like(StringUtils.isNotBlank(artifactId), ModuleDependencyDO::getArtifactId, artifactId)
                .like(StringUtils.isNotBlank(version), ModuleDependencyDO::getVersion, version)
                .orderByDesc(ModuleDependencyDO::getCreateTime);
        
        return queryWrapper;
    }
}
