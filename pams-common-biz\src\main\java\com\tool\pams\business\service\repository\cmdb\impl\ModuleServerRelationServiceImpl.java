package com.tool.pams.business.service.repository.cmdb.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tool.pams.business.service.repository.cmdb.ModuleServerRelationService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleServerRelationQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO;
import com.tool.pams.repository.mapper.cmdb.ModuleServerRelationMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 模块服务器关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
public class ModuleServerRelationServiceImpl extends ServiceImpl<ModuleServerRelationMapper, ModuleServerRelationDO> implements ModuleServerRelationService {

    @Override
    public IPage<ModuleServerRelationDO> page(ModuleServerRelationQueryParamsBO params) {
        return baseMapper.page(params.pageInfo(), params);
    }

    @Override
    public List<ModuleServerRelationDO> getByAppId(Long appId) {
        return this.list(new LambdaQueryWrapper<ModuleServerRelationDO>()
                .eq(ModuleServerRelationDO::getAppId, appId)
                .orderByDesc(ModuleServerRelationDO::getCreateTime));
    }

    @Override
    public List<ModuleServerRelationDO> getBySubAppId(Long subAppId) {
        return this.list(new LambdaQueryWrapper<ModuleServerRelationDO>()
                .eq(ModuleServerRelationDO::getSubAppId, subAppId)
                .orderByDesc(ModuleServerRelationDO::getCreateTime));
    }
}
