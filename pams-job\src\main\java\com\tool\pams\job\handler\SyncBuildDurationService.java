package com.tool.pams.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzed.structure.common.exception.ServiceException;
import com.hzed.structure.log.annotation.TraceId;
import com.hzed.structure.log.util.MdcUtil;
import com.offbytwo.jenkins.JenkinsServer;
import com.offbytwo.jenkins.client.JenkinsHttpClient;
import com.offbytwo.jenkins.model.Build;
import com.offbytwo.jenkins.model.BuildWithDetails;
import com.tool.pams.common.enums.JenkinsBuildParameterType;
import com.tool.pams.repository.domain.cmdb.db.*;
import com.tool.pams.repository.mapper.cmdb.PamsJenkinsPublishModuleRelationMapper;
import com.tool.pams.repository.mapper.cmdb.PamsJenkinsPublishReportMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 同步CMDB
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncBuildDurationService {

    @Resource
    private PamsJenkinsPublishReportMapper pamsAutoPublishRecordMapper;

    @Resource
    private PamsJenkinsPublishModuleRelationMapper pamsJenkinsPublishModuleRelationMapper;

    @Value("${jenkins.url}")
    private String url;

    @Value("${jenkins.username}")
    private String username;

    @Value("${jenkins.password}")
    private String password;

    private final Pattern STARTING_CONTEXT = Pattern.compile("(?s)Starting.*?应用启动成功");
    private final Pattern JOB_PATTERN = Pattern.compile("Starting ([a-zA-Z0-9-]+)-[^\\s]+");
    private final Pattern STARTING_TIME = Pattern.compile("\\[OK\\](.*?)check:OK");
    private final Pattern IP_PATTERN = Pattern.compile("【(\\d{1,3}(?:\\.\\d{1,3}){3})】");

    /**
     * cmdb相关数据同步
     */
    @TraceId("构建时长获取任务")
    @XxlJob("syncBuildDurationHandler")
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> syncBuildDurationHandler(String param) {
        if (StringUtils.isBlank(param)) {
            param = "2";
        }
        XxlJobLogger.log("jenkins发布时间同步开始. traceId:{}", MdcUtil.getTrace());
        ReturnT<String> returnT = ReturnT.SUCCESS;
        List<PamsJenkinsPublishReportDO> pamsJenkinsPublishReportDos = pamsAutoPublishRecordMapper.selectList(new LambdaQueryWrapper<PamsJenkinsPublishReportDO>()
                .isNull(PamsJenkinsPublishReportDO::getDuration)
                .gt(PamsJenkinsPublishReportDO::getCreateTime, LocalDateTime.now().minusDays(Integer.parseInt(param)))
                .lt(PamsJenkinsPublishReportDO::getCreateTime, LocalDateTime.now()));

        if (CollectionUtil.isNotEmpty(pamsJenkinsPublishReportDos)) {
            List<PamsJenkinsPublishModuleRelationDO> pamsJenkinsPublishModuleRelationDos = new ArrayList<>();
            Map<String, List<PamsJenkinsPublishReportDO>> buildMap = pamsJenkinsPublishReportDos.stream()
                    .collect(Collectors.groupingBy(PamsJenkinsPublishReportDO::getBuildId));
            JenkinsServer jenkinsServer = null;
            JenkinsHttpClient jenkinsHttpClient = null;
            try {
                jenkinsServer = createJenkinsServer();
                JenkinsServer finalJenkinsServer = jenkinsServer;

                jenkinsHttpClient = getJenkinsHttpClient();
                JenkinsHttpClient finalJenkinsHttpClient = jenkinsHttpClient;
                buildMap.keySet().forEach(key -> {
                    List<PamsJenkinsPublishReportDO> builds = buildMap.get(key);
                    try {
                        Integer buildId = splitBuild(key);
                        if (buildId == null) {
                            return;
                        }
                        String appName = builds.get(0).getAppName();
                        Build buildByNumber = finalJenkinsServer.getJob(appName).getBuildByNumber(buildId);
                        if (buildByNumber == null) {
                            log.info("appName[{}]查询build为空", appName);
                            return;
                        }
                        String jenkinsBuildJson = getJenkinsBuildJson(finalJenkinsHttpClient, appName, buildId);

                        long duration = buildByNumber.details().getDuration();
                        Map<String, List<String>> moduleBuildMap = computerJobStartTime(buildByNumber.details(), appName);
                        builds.forEach(pamsJenkinsPublishReportDO -> {
                            pamsJenkinsPublishReportDO.setDuration(duration);
                            pamsJenkinsPublishReportDO.setJobParameter(jenkinsBuildJson);
                            List<String> buildInfos = moduleBuildMap.get(pamsJenkinsPublishReportDO.getSubAppName());
                            for (String buildInfo : buildInfos) {
                                String[] ipAndTime = buildInfo.split("\\|");
                                if (ipAndTime.length == 2) {
                                    Long moduleBuildTime = Long.valueOf(ipAndTime[1]);
                                    pamsJenkinsPublishModuleRelationDos.add(PamsJenkinsPublishModuleRelationDO
                                            .builder()
                                            .publishId(pamsJenkinsPublishReportDO.getId())
                                            .moduleCode(pamsJenkinsPublishReportDO.getSubAppName())
                                            .appId(pamsJenkinsPublishReportDO.getAppId())
                                            .buildId(pamsJenkinsPublishReportDO.getBuildId())
                                            .ip(ipAndTime[0])
                                            .duration(moduleBuildTime)
                                            .build());
                                }
                            }
                        });
                    } catch (IOException e) {
                        log.error("获取构建信息异常：{}", e);
                    }

                });
            } finally {
                if (jenkinsServer != null && jenkinsServer.isRunning()) {
                    jenkinsServer.close();
                }
                if (jenkinsHttpClient != null) {
                    jenkinsHttpClient.close();
                }
            }

            pamsAutoPublishRecordMapper.updateById(pamsJenkinsPublishReportDos);
            pamsJenkinsPublishModuleRelationMapper.insertOrUpdate(pamsJenkinsPublishModuleRelationDos);
        }

        XxlJobLogger.log("jenkins发布时间同步结束");

        return returnT;
    }


    /**
     * 获取jenkins连接
     *
     * @return
     */
    public JenkinsServer createJenkinsServer() {
        JenkinsServer jenkinsServer = null;
        try {
            if (StringUtils.isNotBlank(url)) {
                jenkinsServer = new JenkinsServer(new URI(url), username, password);
                if (jenkinsServer.isRunning()) {
                    return jenkinsServer;
                }
            }


        } catch (Exception e) {
            throw new ServiceException("Jenkins连接失败", e);
        }
        throw new ServiceException("Jenkins连接失败,请检查Jenkins配置信息");
    }

    /**
     * Http 客户端工具
     * 如果有些 API 该Jar工具包未提供，可以用此Http客户端操作远程接口，执行命令
     *
     * @return
     */
    public JenkinsHttpClient getJenkinsHttpClient() {
        JenkinsHttpClient jenkinsHttpClient = null;

        try {
            if (StringUtils.isNotBlank(url)) {
                jenkinsHttpClient = new JenkinsHttpClient(new URI(url), username, password);
                jenkinsHttpClient.get("/");
                return jenkinsHttpClient;
            }
        } catch (Exception e) {
            throw new ServiceException("Jenkins连接失败", e);
        }
        throw new ServiceException("Jenkins HTTP连接失败");
    }


    public Integer splitBuild(String param) {
        String[] split = param.split("_");
        try {
            return Integer.valueOf(split[split.length - 1]);
        } catch (Exception e) {
            log.info("格式化构建id失败:{}", param);
            return null;
        }
    }

    public Map<String, List<String>> computerJobStartTime(BuildWithDetails buildWithDetails, String jobName) {
        Map<String, List<String>> result = new HashMap<>(16);
        try {
            String[] split = buildWithDetails.getConsoleOutputText().split("\\[" + jobName + "]");
            String content = "";
            if (split.length > 1) {
                content = split[split.length - 1];
            }
            // 获取构建内容
            Matcher matcher = STARTING_CONTEXT.matcher(content);

            List<String> matched = new ArrayList<>();
            while (matcher.find()) {
                matched.add(matcher.group().replaceAll("\\s+", " ").trim());
            }
            for (String build : matched) {
                String moduleName = "";
                Matcher moduleMatcher = JOB_PATTERN.matcher(build);
                if (moduleMatcher.find()) {
                    moduleName = moduleMatcher.group(1);
                }

                String ip = "";
                Matcher ipMatcher = IP_PATTERN.matcher(build);
                if (ipMatcher.find()) {
                    ip = ipMatcher.group(1);
                }
                Matcher m = STARTING_TIME.matcher(build);
                if (m.find()) {
                    String group = m.group();
                    long count = group.chars().filter(c -> c == '.').count();
                    String value = ip +"|" + count;
                    result.computeIfAbsent(moduleName, k -> new ArrayList<>()).add(value);
                }
            }
        } catch (Exception e) {
            log.error("获取启动时长失败,应用名:{}", jobName);
        }
        return result;
    }


    public String getBuildParam(String actionsStr) {
        JSONArray actions = JSON.parseArray(actionsStr);
        if (actions != null) {
            for (int i = 0; i < actions.size(); i++) {
                JSONObject object = actions.getJSONObject(i);
                JSONArray params = object.getJSONArray("parameters");
                if (CollectionUtil.isNotEmpty(params)) {
                    for (int j = 0; j < params.size(); j++) {
                        JSONObject paramObject = params.getJSONObject(j);
                        paramObject.put("parameterType", JenkinsBuildParameterType.getType(paramObject.getString("_class")));
                    }
                    return params.toJSONString();
                }

            }
        }
        return null;
    }


    /**
     * @param jenkinsHttpClient jenkins Http连接
     * @param jobName           job名
     * <AUTHOR>
     * @date 2025/3/4 14:24
     * @Description 获取构建记录详情json
     * @MethodName getJenkinsStages
     */
    public String getJenkinsBuildJson(JenkinsHttpClient jenkinsHttpClient, String jobName, Integer buildNum) {
        if (jenkinsHttpClient == null) {
            return null;
        }
        try {
            String job = jenkinsHttpClient.get("/job/" + jobName + "/" + buildNum + "/api/json");
            JSONObject jsonObject = JSON.parseObject(job);
            JSONArray actions = jsonObject.getJSONArray("actions");
            return getBuildParam(JSON.toJSONString(actions));
        } catch (Exception e) {
            log.error("jenkins请求获取构建参数异常！", e);
        }
        return null;

    }
}
