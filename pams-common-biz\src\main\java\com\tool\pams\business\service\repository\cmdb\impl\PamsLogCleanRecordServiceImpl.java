package com.tool.pams.business.service.repository.cmdb.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.tool.pams.business.service.repository.cmdb.PamsLogCleanRecordService;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordUpdateBO;
import com.tool.pams.repository.domain.cmdb.db.PamsLogCleanRecordDO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordPageVO;
import com.tool.pams.repository.mapper.cmdb.PamsLogCleanRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 应用日志清理记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@Service
public class PamsLogCleanRecordServiceImpl extends ServiceImpl<PamsLogCleanRecordMapper, PamsLogCleanRecordDO> implements PamsLogCleanRecordService {

    @Resource
    private PamsLogCleanRecordMapper pamsLogCleanRecordMapper;

    @Override
    public Boolean saveInfo(PamsLogCleanRecordSaveBO saveBO) {
        PamsLogCleanRecordDO entity = new PamsLogCleanRecordDO();
        BeanUtils.copyProperties(saveBO, entity);
        return SqlHelper.retBool(pamsLogCleanRecordMapper.insert(entity));
    }

    @Override
    public Boolean delInfo(Long id) {
        return SqlHelper.retBool(pamsLogCleanRecordMapper.deleteById(id));
    }

    @Override
    public Boolean updateInfo(PamsLogCleanRecordUpdateBO updateBO) {
        PamsLogCleanRecordDO entity = new PamsLogCleanRecordDO();
        BeanUtils.copyProperties(updateBO, entity);
        return SqlHelper.retBool(pamsLogCleanRecordMapper.updateById(entity));
    }

    @Override
    public PamsLogCleanRecordDetailVO getInfo(Long id) {
        PamsLogCleanRecordDO entity = pamsLogCleanRecordMapper.selectOne(Wrappers.lambdaQuery(PamsLogCleanRecordDO.class).eq(PamsLogCleanRecordDO::getId, id));
        if (entity == null) {
            return null;
        }
        PamsLogCleanRecordDetailVO detailVO = new PamsLogCleanRecordDetailVO();
        BeanUtils.copyProperties(entity, detailVO);
        return detailVO;
    }

    @Override
    public IPage<PamsLogCleanRecordPageVO> getPageInfo(PamsLogCleanRecordQueryParamsBO queryParamsBO) {
        Page<PamsLogCleanRecordDO> pageInfo = queryParamsBO.pageInfo();
        return pamsLogCleanRecordMapper.page(pageInfo, queryParamsBO);
    }
}
