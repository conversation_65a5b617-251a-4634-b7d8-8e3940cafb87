package com.tool.pams.repository.mapper.cmdb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tool.pams.repository.domain.cmdb.bo.ModuleServerRelationQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 模块服务器关系表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Mapper
public interface ModuleServerRelationMapper extends BaseMapper<ModuleServerRelationDO> {

    /**
     * 同步模块服务器关系数据
     *
     * @return
     */
    @DS("cmdb")
    List<ModuleServerRelationDO> syncModuleServerRelation();

    /**
     * 批量插入或更新模块服务器关系信息（使用INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param records 模块服务器关系信息列表
     * @return 受影响的行数
     */
    int insertOrUpdateBatch(@Param("records") List<ModuleServerRelationDO> records);

    /**
     * 查询所有未删除的记录的sub_app_id和server_id组合
     *
     * @return 所有未删除记录的sub_app_id和server_id组合列表
     */
    List<String> selectAllActiveSubAppServerIds();

    /**
     * 批量标记记录为删除状态（根据sub_app_id和server_id组合）
     *
     * @param subAppServerIds 要标记删除的sub_app_id和server_id组合列表
     * @return 受影响的行数
     */
    int markAsDeletedBatchBySubAppServerId(@Param("subAppServerIds") List<String> subAppServerIds);

    /**
     * 分页查询
     *
     * @param pageInfo
     * @param params
     * @return
     */
    IPage<ModuleServerRelationDO> page(Page<ModuleServerRelationDO> pageInfo, @Param("params") ModuleServerRelationQueryParamsBO params);
}
