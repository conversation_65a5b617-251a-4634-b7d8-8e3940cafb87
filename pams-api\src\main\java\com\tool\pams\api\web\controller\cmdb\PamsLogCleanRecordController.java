package com.tool.pams.api.web.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.cmdb.PamsLogCleanRecordService;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordUpdateBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordPageVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 应用日志清理记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Slf4j
@RestController
@RequestMapping("/pamsLogCleanRecord")
@Tag(name = "应用日志清理记录表")
public class PamsLogCleanRecordController {

    @Resource
    private PamsLogCleanRecordService pamsLogCleanRecordService;

    @ApiResponse
    @Operation(summary = "根据id查询应用日志清理记录")
    @PrintLog("根据id查询应用日志清理记录")
    @GetMapping("/info/{id}")
    public PamsLogCleanRecordDetailVO info(@PathVariable Long id) {
        return pamsLogCleanRecordService.getInfo(id);
    }

    @ApiResponse
    @Operation(summary = "分页查询应用日志清理记录")
    @PrintLog("分页查询应用日志清理记录")
    @GetMapping("/page")
    public IPage<PamsLogCleanRecordPageVO> page(@Valid @ParameterObject PamsLogCleanRecordQueryParamsBO queryParamsBO) {
        return pamsLogCleanRecordService.getPageInfo(queryParamsBO);
    }

    @ApiResponse
    @Operation(summary = "保存应用日志清理记录")
    @PrintLog("保存应用日志清理记录")
    @PostMapping("/save")
    public Boolean save(@RequestBody @Valid PamsLogCleanRecordSaveBO saveBO) {
        return pamsLogCleanRecordService.saveInfo(saveBO);
    }

    @ApiResponse
    @Operation(summary = "修改应用日志清理记录")
    @PrintLog("修改应用日志清理记录")
    @PostMapping("/update")
    public Boolean update(@RequestBody @Valid PamsLogCleanRecordUpdateBO updateBO) {
        return pamsLogCleanRecordService.updateInfo(updateBO);
    }

    @ApiResponse
    @Operation(summary = "删除应用日志清理记录")
    @PrintLog("删除应用日志清理记录")
    @PostMapping("/delete/{id}")
    public Boolean delete(@PathVariable Long id) {
        return pamsLogCleanRecordService.delInfo(id);
    }
}
