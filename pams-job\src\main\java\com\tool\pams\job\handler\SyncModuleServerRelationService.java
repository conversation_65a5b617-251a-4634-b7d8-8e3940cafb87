package com.tool.pams.job.handler;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hzed.structure.log.annotation.TraceId;
import com.hzed.structure.log.util.MdcUtil;
import com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO;
import com.tool.pams.repository.mapper.cmdb.ModuleServerRelationMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 同步模块服务器关系
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class SyncModuleServerRelationService {

    @Resource
    private ModuleServerRelationMapper moduleServerRelationMapper;

    /**
     * 同步模块服务器关系数据
     */
    @TraceId("同步模块服务器关系数据")
    @XxlJob("syncModuleServerRelationHandler")
    public ReturnT<String> syncModuleServerRelationHandler() {
        XxlJobLogger.log("同步模块服务器关系数据开始. traceId:{}", MdcUtil.getTrace());
        ReturnT<String> returnT = ReturnT.SUCCESS;

        try {
            // 1. 从cmdb_platform数据源查询数据
            List<ModuleServerRelationDO> moduleServerRelations = moduleServerRelationMapper.syncModuleServerRelation();

            // 2. 获取目标数据库中所有未删除的记录的sub_app_id和server_id组合
            List<String> existingSubAppServerIds = moduleServerRelationMapper.selectAllActiveSubAppServerIds();

            if (CollectionUtil.isNotEmpty(moduleServerRelations)) {
                // 3. 处理每条记录，解析JSON中的IP地址
                for (ModuleServerRelationDO relation : moduleServerRelations) {
                    processResourceData(relation);
                }

                // 4. 批量插入或更新到pamsdb数据源
                moduleServerRelationMapper.insertOrUpdateBatch(moduleServerRelations);

                XxlJobLogger.log("成功同步 {} 条模块服务器关系数据", moduleServerRelations.size());

                // 5. 处理删除的数据：找出在目标数据库中存在但在源数据中不存在的记录
                if (CollectionUtil.isNotEmpty(existingSubAppServerIds)) {
                    // 获取源数据中的所有sub_app_id和server_id组合
                    List<String> sourceSubAppServerIds = new java.util.ArrayList<>();
                    for (ModuleServerRelationDO relation : moduleServerRelations) {
                        sourceSubAppServerIds.add(relation.getSubAppId() + "_" + relation.getServerId());
                    }

                    // 找出需要删除的组合（在目标数据库中存在但在源数据中不存在）
                    List<String> subAppServerIdsToDelete = new java.util.ArrayList<>();
                    for (String existingId : existingSubAppServerIds) {
                        if (!sourceSubAppServerIds.contains(existingId)) {
                            subAppServerIdsToDelete.add(existingId);
                        }
                    }

                    if (CollectionUtil.isNotEmpty(subAppServerIdsToDelete)) {
                        moduleServerRelationMapper.markAsDeletedBatchBySubAppServerId(subAppServerIdsToDelete);
                        XxlJobLogger.log("标记删除 {} 条不存在的模块服务器关系数据", subAppServerIdsToDelete.size());
                    }
                }
            } else {
                XxlJobLogger.log("没有需要同步的模块服务器关系数据");

                // 如果源数据为空，但目标数据库有数据，则全部标记为删除
                if (CollectionUtil.isNotEmpty(existingSubAppServerIds)) {
                    moduleServerRelationMapper.markAsDeletedBatchBySubAppServerId(existingSubAppServerIds);
                    XxlJobLogger.log("源数据为空，标记删除所有 {} 条模块服务器关系数据", existingSubAppServerIds.size());
                }
            }
            
        } catch (Exception e) {
            log.error("同步模块服务器关系数据异常：{}", e.getMessage(), e);
            XxlJobLogger.log("同步模块服务器关系数据异常：{}", e.getMessage());
            returnT = ReturnT.FAIL;
        }

        XxlJobLogger.log("同步模块服务器关系数据结束");
        return returnT;
    }

    /**
     * 处理资源数据，解析JSON中的IP地址
     */
    private void processResourceData(ModuleServerRelationDO relation) {
        String resourceDataJson = relation.getResourceData();

        // 如果private_ip为空，尝试从JSON中解析
        if (StringUtils.isBlank(relation.getPrivateIp()) && StringUtils.isNotBlank(resourceDataJson)) {
            String privateIp = parsePrivateIpFromJson(resourceDataJson);
            relation.setPrivateIp(privateIp);
        }

        // 如果public_ip为空，尝试从JSON中解析
        if (StringUtils.isBlank(relation.getPublicIp()) && StringUtils.isNotBlank(resourceDataJson)) {
            String publicIp = parsePublicIpFromJson(resourceDataJson);
            relation.setPublicIp(publicIp);
        }

        // 清除临时的resourceData字段，避免插入数据库
        relation.setResourceData(null);
    }

    /**
     * 从JSON数据中解析私网IP地址
     */
    private String parsePrivateIpFromJson(String resourceDataJson) {
        if (StringUtils.isBlank(resourceDataJson)) {
            return "";
        }
        
        try {
            JSONObject resourceData = JSON.parseObject(resourceDataJson);

            // 解析私网IP地址
            JSONObject networkInterfaces = resourceData.getJSONObject("networkInterfaces");
            if (networkInterfaces != null) {
                JSONArray networkInterfaceArray = networkInterfaces.getJSONArray("networkInterface");
                if (networkInterfaceArray != null && !networkInterfaceArray.isEmpty()) {
                    JSONObject firstInterface = networkInterfaceArray.getJSONObject(0);
                    JSONObject privateIpSets = firstInterface.getJSONObject("privateIpSets");
                    if (privateIpSets != null) {
                        JSONArray privateIpSetArray = privateIpSets.getJSONArray("privateIpSet");
                        if (privateIpSetArray != null && !privateIpSetArray.isEmpty()) {
                            JSONObject firstPrivateIp = privateIpSetArray.getJSONObject(0);
                            return firstPrivateIp.getString("privateIpAddress");
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析私网IP地址失败：{}", e.getMessage());
        }
        
        return "";
    }

    /**
     * 从JSON数据中解析公网IP地址
     */
    private String parsePublicIpFromJson(String resourceDataJson) {
        if (StringUtils.isBlank(resourceDataJson)) {
            return "";
        }
        
        try {
            JSONObject resourceData = JSON.parseObject(resourceDataJson);

            // 解析公网IP地址
            JSONObject publicIpAddress = resourceData.getJSONObject("publicIpAddress");
            if (publicIpAddress != null) {
                JSONArray ipAddressArray = publicIpAddress.getJSONArray("ipAddress");
                if (ipAddressArray != null && ipAddressArray.size() > 0) {
                    return ipAddressArray.getString(0);
                }
            }

            // 如果publicIpAddress为空，尝试获取snatPublicIpAddress
            JSONArray snatPublicIpAddressArray = resourceData.getJSONArray("snatPublicIpAddress");
            if (snatPublicIpAddressArray != null && snatPublicIpAddressArray.size() > 0) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < snatPublicIpAddressArray.size(); i++) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    sb.append(snatPublicIpAddressArray.getString(i));
                }
                return sb.toString();
            }

            // 如果是字符串类型的snatPublicIpAddress
            String snatPublicIpAddress = resourceData.getString("snatPublicIpAddress");
            if (StringUtils.isNotBlank(snatPublicIpAddress)) {
                return snatPublicIpAddress;
            }
            
        } catch (Exception e) {
            log.warn("解析公网IP地址失败：{}", e.getMessage());
        }
        
        return "";
    }
}
