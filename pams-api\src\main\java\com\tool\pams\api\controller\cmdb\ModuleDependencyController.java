package com.tool.pams.api.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.result.Result;
import com.tool.pams.business.service.repository.cmdb.ModuleDependencyService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleDependencyQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 模块依赖表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@RestController
@RequestMapping("/cmdb/module-dependency")
@Tag(name = "模块依赖管理", description = "模块依赖相关接口")
public class ModuleDependencyController {

    @Resource
    private ModuleDependencyService moduleDependencyService;

    @PostMapping("/page")
    @Operation(summary = "分页查询模块依赖")
    public Result<IPage<ModuleDependencyDO>> page(@RequestBody ModuleDependencyQueryParamsBO params) {
        IPage<ModuleDependencyDO> result = moduleDependencyService.page(params);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询模块依赖")
    public Result<ModuleDependencyDO> getById(@Parameter(description = "主键ID") @PathVariable Long id) {
        ModuleDependencyDO result = moduleDependencyService.getById(id);
        return Result.success(result);
    }

    @GetMapping("/by-app/{appId}")
    @Operation(summary = "根据应用ID查询模块依赖")
    public Result<List<ModuleDependencyDO>> getByAppId(@Parameter(description = "应用ID") @PathVariable Long appId) {
        List<ModuleDependencyDO> result = moduleDependencyService.getByAppId(appId);
        return Result.success(result);
    }

    @GetMapping("/by-group-artifact")
    @Operation(summary = "根据groupId和artifactId查询模块依赖")
    public Result<List<ModuleDependencyDO>> getByGroupIdAndArtifactId(
            @Parameter(description = "groupId") @RequestParam String groupId,
            @Parameter(description = "artifactId") @RequestParam String artifactId) {
        List<ModuleDependencyDO> result = moduleDependencyService.getByGroupIdAndArtifactId(groupId, artifactId);
        return Result.success(result);
    }

    @PostMapping
    @Operation(summary = "新增模块依赖")
    public Result<Boolean> save(@RequestBody ModuleDependencyDO moduleDependency) {
        boolean result = moduleDependencyService.save(moduleDependency);
        return Result.success(result);
    }

    @PutMapping
    @Operation(summary = "更新模块依赖")
    public Result<Boolean> update(@RequestBody ModuleDependencyDO moduleDependency) {
        boolean result = moduleDependencyService.updateById(moduleDependency);
        return Result.success(result);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除模块依赖")
    public Result<Boolean> delete(@Parameter(description = "主键ID") @PathVariable Long id) {
        boolean result = moduleDependencyService.removeById(id);
        return Result.success(result);
    }
}
