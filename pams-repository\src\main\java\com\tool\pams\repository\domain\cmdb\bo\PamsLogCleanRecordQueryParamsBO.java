package com.tool.pams.repository.domain.cmdb.bo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.common.PageParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsLogCleanRecordDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用日志清理记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsLogCleanRecordQueryParamsBO对象", description = "应用日志清理记录表")
public class PamsLogCleanRecordQueryParamsBO extends PageParamsBO<PamsLogCleanRecordDO> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "应用ID")
    private Long appId;

    @Schema(description = "服务器IP")
    private String serverIp;

    @Schema(description = "清理文件路径")
    private String cleanFilePath;

    @Schema(description = "清理文件名称")
    private String cleanFileName;

    @Schema(description = "清理时间开始")
    private LocalDateTime cleanTimeStart;

    @Schema(description = "清理时间结束")
    private LocalDateTime cleanTimeEnd;

    @Schema(description = "操作类型(MANUAL=手动清理,SCHEDULED=定时任务)")
    private String operationType;

    @Schema(description = "清理原因(THRESHOLD=文件达到阈值,SCHEDULED_TIME=达到固定清理时间,MANUAL=手动清理)")
    private String cleanReason;

    @Schema(description = "操作结果(SUCCESS=成功,FAILED=失败)")
    private String operationResult;

    @Override
    public LambdaQueryWrapper<PamsLogCleanRecordDO> queryWrapper() {
        LambdaQueryWrapper<PamsLogCleanRecordDO> query = new LambdaQueryWrapper<>();

        query.eq(appId != null, PamsLogCleanRecordDO::getAppId, appId)
             .like(StringUtils.isNotBlank(serverIp), PamsLogCleanRecordDO::getServerIp, serverIp)
             .like(StringUtils.isNotBlank(cleanFilePath), PamsLogCleanRecordDO::getCleanFilePath, cleanFilePath)
             .like(StringUtils.isNotBlank(cleanFileName), PamsLogCleanRecordDO::getCleanFileName, cleanFileName)
             .ge(cleanTimeStart != null, PamsLogCleanRecordDO::getCleanTime, cleanTimeStart)
             .le(cleanTimeEnd != null, PamsLogCleanRecordDO::getCleanTime, cleanTimeEnd)
             .eq(StringUtils.isNotBlank(operationType), PamsLogCleanRecordDO::getOperationType, operationType)
             .eq(StringUtils.isNotBlank(cleanReason), PamsLogCleanRecordDO::getCleanReason, cleanReason)
             .eq(StringUtils.isNotBlank(operationResult), PamsLogCleanRecordDO::getOperationResult, operationResult);

        return query;
    }
}
