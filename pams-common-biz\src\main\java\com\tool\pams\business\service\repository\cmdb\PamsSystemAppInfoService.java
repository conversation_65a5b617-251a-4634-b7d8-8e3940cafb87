package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import com.tool.pams.repository.domain.cmdb.bo.PamsSystemAppInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsSystemAppInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsSystemAppInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.ModuleInfoVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsSystemAppInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsSystemAppInfoPageVO;

/**
 * <p>
 * 系统应用信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
public interface PamsSystemAppInfoService extends IService<PamsSystemAppInfoDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(PamsSystemAppInfoSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(PamsSystemAppInfoUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    PamsSystemAppInfoDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<PamsSystemAppInfoPageVO> getPageInfo(PamsSystemAppInfoQueryParamsBO queryParamsBO);

    /**
     * 分页查询模块信息
     *
     * @param queryParamsBO 查询参数
     * @return 模块信息分页结果
     */
    IPage<ModuleInfoVO> getModulePageInfo(ModuleInfoQueryParamsBO queryParamsBO);

}
