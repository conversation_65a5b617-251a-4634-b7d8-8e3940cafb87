package com.tool.pams.api.web.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.cmdb.ModuleServerRelationService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleServerRelationQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 模块服务器关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@RestController
@RequestMapping("/cmdb/module-server-relation")
@Tag(name = "模块服务器关系管理", description = "模块服务器关系相关接口")
public class ModuleServerRelationController {

    @Resource
    private ModuleServerRelationService moduleServerRelationService;

    @ApiResponse
    @Operation(summary = "分页查询模块服务器关系")
    @PrintLog("分页查询模块服务器关系")
    @GetMapping("/page")
    public IPage<ModuleServerRelationDO> page(@Valid @ParameterObject ModuleServerRelationQueryParamsBO params) {
        return moduleServerRelationService.page(params);
    }

    @ApiResponse
    @Operation(summary = "根据ID查询模块服务器关系")
    @PrintLog("根据ID查询模块服务器关系")
    @GetMapping("/info/{id}")
    public ModuleServerRelationDO getById(@Parameter(description = "主键ID") @PathVariable Long id) {
        return moduleServerRelationService.getById(id);
    }

    @ApiResponse
    @Operation(summary = "根据应用ID查询模块服务器关系")
    @PrintLog("根据应用ID查询模块服务器关系")
    @GetMapping("/by-app/{appId}")
    public List<ModuleServerRelationDO> getByAppId(@Parameter(description = "应用ID") @PathVariable Long appId) {
        return moduleServerRelationService.getByAppId(appId);
    }

    @ApiResponse
    @Operation(summary = "根据子应用ID查询模块服务器关系")
    @PrintLog("根据子应用ID查询模块服务器关系")
    @GetMapping("/by-sub-app/{subAppId}")
    public List<ModuleServerRelationDO> getBySubAppId(@Parameter(description = "子应用ID(模块ID)") @PathVariable Long subAppId) {
        return moduleServerRelationService.getBySubAppId(subAppId);
    }
}
