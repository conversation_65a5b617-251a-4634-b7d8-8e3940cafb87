-- 模块管理相关表结构

-- 第一张表：模块服务器关系表
CREATE TABLE `t_module_server_relation` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `sub_app_id` bigint(20) NOT NULL COMMENT '子应用id(模块id)',
  `server_id` bigint(20) DEFAULT NULL COMMENT '服务器id',
  `server_name` varchar(255) DEFAULT NULL COMMENT '服务器名称',
  `private_ip` varchar(50) DEFAULT NULL COMMENT '私网ip',
  `public_ip` varchar(50) DEFAULT NULL COMMENT '公网ip',
  `deploy_port` int(11) DEFAULT NULL COMMENT '部署端口',
  `deploy_path` varchar(500) DEFAULT NULL COMMENT '部署路径',
  `jvm_params` text COMMENT 'JVM参数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(100) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
  PRIMARY KEY (`id`),
  KEY `idx_sub_app_id` (`sub_app_id`),
  KEY `idx_server_id` (`server_id`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模块服务器关系表';

-- 第二张表：模块依赖表
CREATE TABLE `t_module_dependency` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `app_id` bigint(20) NOT NULL COMMENT '应用id',
  `group_id` varchar(255) NOT NULL COMMENT 'groupId',
  `artifact_id` varchar(255) NOT NULL COMMENT 'artifactId',
  `version` varchar(100) NOT NULL COMMENT '版本',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `creator` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updater` varchar(100) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 0未删除 1已删除',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_group_artifact` (`group_id`, `artifact_id`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模块依赖表';
