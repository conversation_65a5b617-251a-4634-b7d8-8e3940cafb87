package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordUpdateBO;
import com.tool.pams.repository.domain.cmdb.db.PamsLogCleanRecordDO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordPageVO;

/**
 * <p>
 * 应用日志清理记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
public interface PamsLogCleanRecordService extends IService<PamsLogCleanRecordDO> {

    /**
     * 添加信息
     *
     * @param saveBO
     * @return
     */
    Boolean saveInfo(PamsLogCleanRecordSaveBO saveBO);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    Boolean delInfo(Long id);

    /**
     * 修改信息
     *
     * @param updateBO
     * @return
     */
    Boolean updateInfo(PamsLogCleanRecordUpdateBO updateBO);

    /**
     * 通过ID获取信息
     *
     * @param id
     * @return
     */
    PamsLogCleanRecordDetailVO getInfo(Long id);

    /**
     * 分页获取列表
     *
     * @param queryParamsBO
     * @return
     */
    IPage<PamsLogCleanRecordPageVO> getPageInfo(PamsLogCleanRecordQueryParamsBO queryParamsBO);
}
