<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.PamsLogCleanRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.PamsLogCleanRecordDO">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="server_ip" property="serverIp" />
        <result column="clean_file_path" property="cleanFilePath" />
        <result column="clean_file_name" property="cleanFileName" />
        <result column="clean_time" property="cleanTime" />
        <result column="operation_type" property="operationType" />
        <result column="clean_reason" property="cleanReason" />
        <result column="operation_result" property="operationResult" />
        <result column="failure_reason" property="failureReason" />
        <result column="size_before_clean" property="sizeBeforeClean" />
        <result column="size_after_clean" property="sizeAfterClean" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, server_ip, clean_file_path, clean_file_name, clean_time, operation_type, clean_reason, operation_result, failure_reason, size_before_clean, size_after_clean, create_time, update_time, creator, updater, deleted
    </sql>

    <select id="page" resultType="com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordPageVO">
        SELECT
            id,
            app_id,
            server_ip,
            clean_file_path,
            clean_file_name,
            clean_time,
            operation_type,
            clean_reason,
            operation_result,
            failure_reason,
            size_before_clean,
            size_after_clean,
            create_time,
            update_time
        FROM t_pams_log_clean_record
        WHERE deleted = 0
        <if test="params.appId != null">
            AND app_id = #{params.appId}
        </if>
        <if test="params.serverIp != null and params.serverIp != ''">
            AND server_ip LIKE CONCAT('%', #{params.serverIp}, '%')
        </if>
        <if test="params.cleanFilePath != null and params.cleanFilePath != ''">
            AND clean_file_path LIKE CONCAT('%', #{params.cleanFilePath}, '%')
        </if>
        <if test="params.cleanFileName != null and params.cleanFileName != ''">
            AND clean_file_name LIKE CONCAT('%', #{params.cleanFileName}, '%')
        </if>
        <if test="params.cleanTimeStart != null">
            AND clean_time >= #{params.cleanTimeStart}
        </if>
        <if test="params.cleanTimeEnd != null">
            AND clean_time &lt;= #{params.cleanTimeEnd}
        </if>
        <if test="params.operationType != null and params.operationType != ''">
            AND operation_type = #{params.operationType}
        </if>
        <if test="params.cleanReason != null and params.cleanReason != ''">
            AND clean_reason = #{params.cleanReason}
        </if>
        <if test="params.operationResult != null and params.operationResult != ''">
            AND operation_result = #{params.operationResult}
        </if>
        ORDER BY clean_time DESC
    </select>

</mapper>
