package com.tool.pams.business.service.repository.cmdb.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.tool.pams.business.service.repository.cmdb.ModuleDependencyService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleDependencyQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO;
import com.tool.pams.repository.mapper.cmdb.ModuleDependencyMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 模块依赖表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Service
public class ModuleDependencyServiceImpl extends ServiceImpl<ModuleDependencyMapper, ModuleDependencyDO> implements ModuleDependencyService {

    @Override
    public IPage<ModuleDependencyDO> page(ModuleDependencyQueryParamsBO params) {
        return baseMapper.page(params.pageInfo(), params);
    }

    @Override
    public List<ModuleDependencyDO> getByAppId(Long appId) {
        return this.list(new LambdaQueryWrapper<ModuleDependencyDO>()
                .eq(ModuleDependencyDO::getAppId, appId)
                .orderByDesc(ModuleDependencyDO::getCreateTime));
    }

    @Override
    public List<ModuleDependencyDO> getBySubAppId(Long subAppId) {
        return this.list(new LambdaQueryWrapper<ModuleDependencyDO>()
                .eq(ModuleDependencyDO::getSubAppId, subAppId)
                .orderByDesc(ModuleDependencyDO::getCreateTime));
    }
}
