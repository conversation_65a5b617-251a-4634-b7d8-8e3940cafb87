package com.tool.pams.repository.mapper.cmdb;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tool.pams.repository.domain.cmdb.bo.PamsBusinessSystemInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoExtendDO;
import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import com.tool.pams.repository.domain.cmdb.vo.PamsBusinessSystemInfoPageExtendVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 新业务系统资源信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-05 11:13:24
 */
@Mapper
public interface PamsBusinessSystemInfoMapper extends BaseMapper<PamsBusinessSystemInfoDO> {

    /**
     * 同步应用
     *
     * @return
     */
    @DS("cmdb")
    List<PamsBusinessSystemInfoDO> syncBusinessSystemInfo();

    /**
     * 批量插入或更新业务系统信息（使用INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param records 业务系统信息列表
     * @return 受影响的行数
     */
    int insertOrUpdateBatch(@Param("records") List<PamsBusinessSystemInfoDO> records);

    /**
     * 分页查询
     *
     * @param pageInfo
     * @param params
     * @return
     */
    IPage<PamsBusinessSystemInfoExtendDO> page(Page<PamsBusinessSystemInfoDO> pageInfo, @Param("params") PamsBusinessSystemInfoQueryParamsBO params);

}
