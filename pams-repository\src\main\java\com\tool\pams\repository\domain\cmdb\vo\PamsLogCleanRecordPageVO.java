package com.tool.pams.repository.domain.cmdb.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用日志清理记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsLogCleanRecordPageVO对象", description = "应用日志清理记录表")
public class PamsLogCleanRecordPageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "应用ID")
    private Long appId;

    @Schema(description = "服务器IP")
    private String serverIp;

    @Schema(description = "清理文件路径")
    private String cleanFilePath;

    @Schema(description = "清理文件名称")
    private String cleanFileName;

    @Schema(description = "清理时间")
    private LocalDateTime cleanTime;

    @Schema(description = "操作类型(MANUAL=手动清理,SCHEDULED=定时任务)")
    private String operationType;

    @Schema(description = "清理原因(THRESHOLD=文件达到阈值,SCHEDULED_TIME=达到固定清理时间,MANUAL=手动清理)")
    private String cleanReason;

    @Schema(description = "操作结果(SUCCESS=成功,FAILED=失败)")
    private String operationResult;

    @Schema(description = "失败原因")
    private String failureReason;

    @Schema(description = "清理前大小(字节)")
    private Long sizeBeforeClean;

    @Schema(description = "清理后大小(字节)")
    private Long sizeAfterClean;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;
}
