package com.tool.pams.repository.domain.cmdb.db;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 模块服务器关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_module_server_relation")
@Schema(name = "ModuleServerRelationDO对象", description = "模块服务器关系表")
public class ModuleServerRelationDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "`id`", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "应用id")
    @TableField("`app_id`")
    private Long appId;

    @Schema(description = "子应用id(模块id)")
    @TableField("`sub_app_id`")
    private Long subAppId;

    @Schema(description = "服务器id")
    @TableField("`server_id`")
    private Long serverId;

    @Schema(description = "服务器名称")
    @TableField("`server_name`")
    private String serverName;

    @Schema(description = "私网ip")
    @TableField("`private_ip`")
    private String privateIp;

    @Schema(description = "公网ip")
    @TableField("`public_ip`")
    private String publicIp;

    @Schema(description = "部署端口")
    @TableField("`deploy_port`")
    private Integer deployPort;

    @Schema(description = "部署路径")
    @TableField("`deploy_path`")
    private String deployPath;

    @Schema(description = "JVM参数")
    @TableField("`jvm_params`")
    private String jvmParams;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;

    @Schema(description = "资源数据JSON（仅用于同步时临时存储）")
    @TableField(exist = false)
    private String resourceData;
}
