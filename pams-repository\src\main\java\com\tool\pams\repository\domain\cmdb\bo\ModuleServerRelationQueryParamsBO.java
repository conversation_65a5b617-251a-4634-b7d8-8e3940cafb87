package com.tool.pams.repository.domain.cmdb.bo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO;
import com.tool.pams.repository.domain.common.PageParamsBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <p>
 * 模块服务器关系表查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ModuleServerRelationQueryParamsBO对象", description = "模块服务器关系表查询参数")
public class ModuleServerRelationQueryParamsBO extends PageParamsBO<ModuleServerRelationDO> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "应用id")
    private Long appId;

    @Schema(description = "子应用id(模块id)")
    private Long subAppId;

    @Schema(description = "服务器id")
    private Long serverId;

    @Schema(description = "服务器名称")
    private String serverName;

    @Schema(description = "私网ip")
    private String privateIp;

    @Schema(description = "公网ip")
    private String publicIp;

    @Schema(description = "部署端口")
    private Integer deployPort;

    @Schema(description = "部署路径")
    private String deployPath;
}
