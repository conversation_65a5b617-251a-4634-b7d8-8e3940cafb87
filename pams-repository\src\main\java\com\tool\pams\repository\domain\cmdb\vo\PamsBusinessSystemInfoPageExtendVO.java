package com.tool.pams.repository.domain.cmdb.vo;

import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoDO;
import com.tool.pams.repository.domain.cmdb.db.PamsBusinessSystemInfoExtendDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 新业务系统资源信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09 09:34:16
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsBusinessSystemInfoPageExtendVO对象", description = "新业务系统资源信息扩展表")
public class PamsBusinessSystemInfoPageExtendVO extends PamsBusinessSystemInfoPageVO implements Serializable{

    private static final long serialVersionUID = 1L;

    @Schema(description = "机构名称")
    private String organizationName;

    @Schema(description = "开发负责组")
    private String devTeam;

    @Schema(description = "测试负责组")
    private String testTeam;

    @Schema(description = "负责项目经理")
    private String projectManager;

    @Schema(description = "应用数量")
    private Long appNum;


    public static PamsBusinessSystemInfoPageExtendVO of(PamsBusinessSystemInfoExtendDO entity){
        if(entity == null){
            return null;
        }
        PamsBusinessSystemInfoPageExtendVO pageVO = new PamsBusinessSystemInfoPageExtendVO();
        BeanUtils.copyProperties(entity,pageVO);
        return pageVO;
    }

}
