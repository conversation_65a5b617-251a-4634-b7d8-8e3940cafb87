package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleDependencyQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO;

/**
 * <p>
 * 模块依赖表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface ModuleDependencyService extends IService<ModuleDependencyDO> {

    /**
     * 分页查询
     *
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<ModuleDependencyDO> page(ModuleDependencyQueryParamsBO params);

    /**
     * 根据应用ID查询模块依赖
     *
     * @param appId 应用ID
     * @return 模块依赖列表
     */
    java.util.List<ModuleDependencyDO> getByAppId(Long appId);

    /**
     * 根据子应用ID查询模块依赖
     *
     * @param subAppId 子应用ID(模块ID)
     * @return 模块依赖列表
     */
    java.util.List<ModuleDependencyDO> getBySubAppId(Long subAppId);
}
