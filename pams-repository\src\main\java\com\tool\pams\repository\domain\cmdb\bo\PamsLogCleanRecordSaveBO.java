package com.tool.pams.repository.domain.cmdb.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 应用日志清理记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "PamsLogCleanRecordSaveBO对象", description = "应用日志清理记录表")
public class PamsLogCleanRecordSaveBO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "应用ID")
    @NotNull(message = "应用ID不能为空")
    private Long appId;

    @Schema(description = "服务器IP")
    @NotBlank(message = "服务器IP不能为空")
    private String serverIp;

    @Schema(description = "清理文件路径")
    @NotBlank(message = "清理文件路径不能为空")
    private String cleanFilePath;

    @Schema(description = "清理文件名称")
    @NotBlank(message = "清理文件名称不能为空")
    private String cleanFileName;

    @Schema(description = "清理时间")
    @NotNull(message = "清理时间不能为空")
    private LocalDateTime cleanTime;

    @Schema(description = "操作类型(MANUAL=手动清理,SCHEDULED=定时任务)")
    @NotBlank(message = "操作类型不能为空")
    private String operationType;

    @Schema(description = "清理原因(THRESHOLD=文件达到阈值,SCHEDULED_TIME=达到固定清理时间,MANUAL=手动清理)")
    @NotBlank(message = "清理原因不能为空")
    private String cleanReason;

    @Schema(description = "操作结果(SUCCESS=成功,FAILED=失败)")
    @NotBlank(message = "操作结果不能为空")
    private String operationResult;

    @Schema(description = "失败原因")
    private String failureReason;

    @Schema(description = "清理前大小(字节)")
    private Long sizeBeforeClean;

    @Schema(description = "清理后大小(字节)")
    private Long sizeAfterClean;
}
