package com.tool.pams.repository.mapper.cmdb;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tool.pams.repository.domain.cmdb.bo.ModuleDependencyQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 模块依赖表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Mapper
public interface ModuleDependencyMapper extends BaseMapper<ModuleDependencyDO> {

    /**
     * 批量插入或更新模块依赖信息（使用INSERT ... ON DUPLICATE KEY UPDATE）
     *
     * @param records 模块依赖信息列表
     * @return 受影响的行数
     */
    int insertOrUpdateBatch(@Param("records") List<ModuleDependencyDO> records);

    /**
     * 分页查询
     *
     * @param pageInfo
     * @param params
     * @return
     */
    IPage<ModuleDependencyDO> page(Page<ModuleDependencyDO> pageInfo, @Param("params") ModuleDependencyQueryParamsBO params);
}
