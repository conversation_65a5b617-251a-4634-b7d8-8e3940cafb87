package com.tool.pams.repository.domain.cmdb.db;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 模块依赖表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_module_dependency")
@Schema(name = "ModuleDependencyDO对象", description = "模块依赖表")
public class ModuleDependencyDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId(value = "`id`", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "应用id")
    @TableField("`app_id`")
    private Long appId;

    @Schema(description = "子应用id(模块id)")
    @TableField("`sub_app_id`")
    private Long subAppId;

    @Schema(description = "groupId")
    @TableField("`group_id`")
    private String groupId;

    @Schema(description = "artifactId")
    @TableField("`artifact_id`")
    private String artifactId;

    @Schema(description = "版本")
    @TableField("`version`")
    private String version;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;
}
