package com.tool.pams.repository.domain.cmdb.bo;

import com.tool.pams.repository.domain.cmdb.db.PamsSystemAppInfoDO;
import com.tool.pams.repository.domain.common.PageParamsBO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 模块信息查询参数
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ModuleInfoQueryParamsBO对象", description = "模块信息查询参数")
public class ModuleInfoQueryParamsBO extends PageParamsBO<PamsSystemAppInfoDO> implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模块标识")
    private String appCode;

    @Schema(description = "所属应用")
    private String appName;
}
