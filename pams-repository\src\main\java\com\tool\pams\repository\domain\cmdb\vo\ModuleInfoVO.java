package com.tool.pams.repository.domain.cmdb.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 模块信息查询结果VO
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "ModuleInfoVO对象", description = "模块信息查询结果")
public class ModuleInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "模块ID")
    private Long moduleId;

    @Schema(description = "模块标识")
    private String appCode;

    @Schema(description = "所属应用")
    private String appName;

    @Schema(description = "接口数量")
    private Integer apiCount;

    @Schema(description = "部署端口")
    private Integer deployPort;

    @Schema(description = "部署路径")
    private String deployPath;

    @Schema(description = "部署服务器(多个用逗号隔开)")
    private String serverNames;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;
}
