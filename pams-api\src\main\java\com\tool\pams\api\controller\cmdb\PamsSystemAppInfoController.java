package com.tool.pams.api.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.cmdb.PamsSystemAppInfoService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.ModuleInfoVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 系统应用信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@RestController
@RequestMapping("/cmdb/system-app-info")
@Tag(name = "系统应用信息管理", description = "系统应用信息相关接口")
public class PamsSystemAppInfoController {

    @Resource
    private PamsSystemAppInfoService pamsSystemAppInfoService;

    @ApiResponse
    @Operation(summary = "分页查询模块信息")
    @PrintLog("分页查询模块信息")
    @GetMapping("/module/page")
    public IPage<ModuleInfoVO> getModulePage(@Valid @ParameterObject ModuleInfoQueryParamsBO params) {
        return pamsSystemAppInfoService.getModulePageInfo(params);
    }
}
