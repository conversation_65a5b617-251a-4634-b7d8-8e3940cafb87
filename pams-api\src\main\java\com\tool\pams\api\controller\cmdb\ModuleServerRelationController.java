package com.tool.pams.api.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.result.Result;
import com.tool.pams.business.service.repository.cmdb.ModuleServerRelationService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleServerRelationQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 模块服务器关系表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@RestController
@RequestMapping("/cmdb/module-server-relation")
@Tag(name = "模块服务器关系管理", description = "模块服务器关系相关接口")
public class ModuleServerRelationController {

    @Resource
    private ModuleServerRelationService moduleServerRelationService;

    @PostMapping("/page")
    @Operation(summary = "分页查询模块服务器关系")
    public Result<IPage<ModuleServerRelationDO>> page(@RequestBody ModuleServerRelationQueryParamsBO params) {
        IPage<ModuleServerRelationDO> result = moduleServerRelationService.page(params);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询模块服务器关系")
    public Result<ModuleServerRelationDO> getById(@Parameter(description = "主键ID") @PathVariable Long id) {
        ModuleServerRelationDO result = moduleServerRelationService.getById(id);
        return Result.success(result);
    }

    @GetMapping("/by-sub-app/{subAppId}")
    @Operation(summary = "根据子应用ID查询模块服务器关系")
    public Result<List<ModuleServerRelationDO>> getBySubAppId(@Parameter(description = "子应用ID(模块ID)") @PathVariable Long subAppId) {
        List<ModuleServerRelationDO> result = moduleServerRelationService.getBySubAppId(subAppId);
        return Result.success(result);
    }

    @GetMapping("/by-server/{serverId}")
    @Operation(summary = "根据服务器ID查询模块服务器关系")
    public Result<List<ModuleServerRelationDO>> getByServerId(@Parameter(description = "服务器ID") @PathVariable Long serverId) {
        List<ModuleServerRelationDO> result = moduleServerRelationService.getByServerId(serverId);
        return Result.success(result);
    }

    @PostMapping
    @Operation(summary = "新增模块服务器关系")
    public Result<Boolean> save(@RequestBody ModuleServerRelationDO moduleServerRelation) {
        boolean result = moduleServerRelationService.save(moduleServerRelation);
        return Result.success(result);
    }

    @PutMapping
    @Operation(summary = "更新模块服务器关系")
    public Result<Boolean> update(@RequestBody ModuleServerRelationDO moduleServerRelation) {
        boolean result = moduleServerRelationService.updateById(moduleServerRelation);
        return Result.success(result);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除模块服务器关系")
    public Result<Boolean> delete(@Parameter(description = "主键ID") @PathVariable Long id) {
        boolean result = moduleServerRelationService.removeById(id);
        return Result.success(result);
    }
}
