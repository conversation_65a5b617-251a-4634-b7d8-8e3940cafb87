<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.ModuleServerRelationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO">
        <id column="id" property="id" />
        <result column="sub_app_id" property="subAppId" />
        <result column="server_id" property="serverId" />
        <result column="server_name" property="serverName" />
        <result column="private_ip" property="privateIp" />
        <result column="public_ip" property="publicIp" />
        <result column="deploy_port" property="deployPort" />
        <result column="deploy_path" property="deployPath" />
        <result column="jvm_params" property="jvmParams" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, sub_app_id, server_id, server_name, private_ip, public_ip, deploy_port, deploy_path, jvm_params, create_time, update_time, creator, updater, deleted
    </sql>

    <!-- 同步模块服务器关系数据 -->
    <select id="syncModuleServerRelation" resultType="com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO">
        SELECT
            sar.id,
            sar.sub_app_id,
            rd.id as server_id,
            rd.resource_name as server_name,
            rd.private_ip,
            rd.public_ip,
            sar.deploy_port,
            sar.deploy_path,
            sar.jvm_params,
            sar.create_time,
            sar.update_time
        FROM
            t_cmdb_sub_app_server_relation sar
        LEFT JOIN t_cmdb_resources_data rd ON sar.server_id = rd.id
        WHERE sar.is_delete = 0
        ORDER BY sar.create_time
    </select>

    <!-- 批量插入或更新 -->
    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO t_module_server_relation (
            id, sub_app_id, server_id, server_name, private_ip, public_ip, 
            deploy_port, deploy_path, jvm_params, create_time, update_time, creator, updater, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.subAppId}, #{record.serverId}, #{record.serverName}, 
                #{record.privateIp}, #{record.publicIp}, #{record.deployPort}, #{record.deployPath}, 
                #{record.jvmParams}, #{record.createTime}, #{record.updateTime}, 
                #{record.creator}, #{record.updater}, #{record.deleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            sub_app_id = VALUES(sub_app_id),
            server_id = VALUES(server_id),
            server_name = VALUES(server_name),
            private_ip = VALUES(private_ip),
            public_ip = VALUES(public_ip),
            deploy_port = VALUES(deploy_port),
            deploy_path = VALUES(deploy_path),
            jvm_params = VALUES(jvm_params),
            update_time = VALUES(update_time),
            updater = VALUES(updater)
    </insert>

    <!-- 分页查询 -->
    <select id="page" resultType="com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_module_server_relation
        WHERE deleted = 0
        <if test="params.subAppId != null">
            AND sub_app_id = #{params.subAppId}
        </if>
        <if test="params.serverId != null">
            AND server_id = #{params.serverId}
        </if>
        <if test="params.serverName != null and params.serverName != ''">
            AND server_name LIKE CONCAT('%', #{params.serverName}, '%')
        </if>
        <if test="params.privateIp != null and params.privateIp != ''">
            AND private_ip LIKE CONCAT('%', #{params.privateIp}, '%')
        </if>
        <if test="params.publicIp != null and params.publicIp != ''">
            AND public_ip LIKE CONCAT('%', #{params.publicIp}, '%')
        </if>
        <if test="params.deployPort != null">
            AND deploy_port = #{params.deployPort}
        </if>
        <if test="params.deployPath != null and params.deployPath != ''">
            AND deploy_path LIKE CONCAT('%', #{params.deployPath}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
