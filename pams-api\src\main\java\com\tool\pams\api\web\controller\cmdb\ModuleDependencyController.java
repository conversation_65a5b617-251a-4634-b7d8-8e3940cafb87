package com.tool.pams.api.web.controller.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzed.structure.log.annotation.PrintLog;
import com.hzed.structure.tool.annotation.ApiResponse;
import com.tool.pams.business.service.repository.cmdb.ModuleDependencyService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleDependencyQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 模块依赖表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@RestController
@RequestMapping("/cmdb/module-dependency")
@Tag(name = "模块依赖管理", description = "模块依赖相关接口")
public class ModuleDependencyController {

    @Resource
    private ModuleDependencyService moduleDependencyService;

    @ApiResponse
    @Operation(summary = "分页查询模块依赖")
    @PrintLog("分页查询模块依赖")
    @GetMapping("/page")
    public IPage<ModuleDependencyDO> page(@Valid @ParameterObject ModuleDependencyQueryParamsBO params) {
        return moduleDependencyService.page(params);
    }

    @ApiResponse
    @Operation(summary = "根据ID查询模块依赖")
    @PrintLog("根据ID查询模块依赖")
    @GetMapping("/info/{id}")
    public ModuleDependencyDO getById(@Parameter(description = "主键ID") @PathVariable Long id) {
        return moduleDependencyService.getById(id);
    }

    @ApiResponse
    @Operation(summary = "根据应用ID查询模块依赖")
    @PrintLog("根据应用ID查询模块依赖")
    @GetMapping("/by-app/{appId}")
    public List<ModuleDependencyDO> getByAppId(@Parameter(description = "应用ID") @PathVariable Long appId) {
        return moduleDependencyService.getByAppId(appId);
    }

    @ApiResponse
    @Operation(summary = "根据子应用ID查询模块依赖")
    @PrintLog("根据子应用ID查询模块依赖")
    @GetMapping("/by-sub-app/{subAppId}")
    public List<ModuleDependencyDO> getBySubAppId(@Parameter(description = "子应用ID(模块ID)") @PathVariable Long subAppId) {
        return moduleDependencyService.getBySubAppId(subAppId);
    }
}
