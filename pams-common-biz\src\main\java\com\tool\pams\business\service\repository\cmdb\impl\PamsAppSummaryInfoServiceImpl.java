package com.tool.pams.business.service.repository.cmdb.impl;

import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoSaveBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoUpdateBO;
import com.tool.pams.repository.domain.cmdb.bo.PamsAppSummaryInfoQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoDetailVO;
import com.tool.pams.repository.domain.cmdb.vo.PamsAppSummaryInfoPageVO;
import com.tool.pams.repository.domain.cmdb.db.PamsAppSummaryInfoDO;
import com.tool.pams.repository.domain.common.vo.DropdownOptionVO;
import com.tool.pams.repository.mapper.cmdb.PamsAppSummaryInfoMapper;
import com.tool.pams.business.service.repository.cmdb.PamsAppSummaryInfoService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.hzed.structure.common.exception.ServiceException;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.metadata.OrderItem;

import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import javax.annotation.Resource;


/**
 * <p>
 * 应用信息汇总表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-08
 */
@Service
public class PamsAppSummaryInfoServiceImpl extends ServiceImpl<PamsAppSummaryInfoMapper, PamsAppSummaryInfoDO> implements PamsAppSummaryInfoService {

    @Resource
    private PamsAppSummaryInfoMapper pamsAppSummaryInfoMapper;

    @Override
    public Boolean updateInfo(PamsAppSummaryInfoUpdateBO updateBO){
        //判断应用是否存在
        PamsAppSummaryInfoDO entityOld = pamsAppSummaryInfoMapper.selectById(updateBO.getId());
        if (entityOld == null) {
            throw new ServiceException("应用不存在");
        }

        // 创建新实体对象，只设置允许修改的字段
        PamsAppSummaryInfoDO entityNew = new PamsAppSummaryInfoDO();
        entityNew.setId(updateBO.getId());

        // 只允许修改以下字段（手动编辑字段）
        entityNew.setTestTeam(updateBO.getTestTeam());
        entityNew.setStatus(updateBO.getStatus());
        entityNew.setProjectManager(updateBO.getProjectManager());
        entityNew.setType(updateBO.getType());
        entityNew.setOrganizationId(updateBO.getOrganizationId());
        entityNew.setAppMinorCategory(updateBO.getAppMinorCategory());
        entityNew.setAppMajorCategory(updateBO.getAppMajorCategory());
        entityNew.setSourceProjectFlag(updateBO.getSourceProjectFlag());
        entityNew.setSsoSystemFlag(updateBO.getSsoSystemFlag());
        entityNew.setDatabaseFlag(updateBO.getDatabaseFlag());
        entityNew.setOssPrivateBucketFlag(updateBO.getOssPrivateBucketFlag());

        return SqlHelper.retBool(pamsAppSummaryInfoMapper.updateById(entityNew));
    }

    @Override
    public PamsAppSummaryInfoDetailVO getInfo(Long id){
        return pamsAppSummaryInfoMapper.selectDetailById(id);
    }

    @Override
    public IPage<PamsAppSummaryInfoPageVO> getPageInfo(PamsAppSummaryInfoQueryParamsBO queryParamsBO){
        // 创建正确类型的Page对象，包含排序信息
        Page<PamsAppSummaryInfoDO> doPage = queryParamsBO.pageInfo();
        Page<PamsAppSummaryInfoPageVO> voPage = new Page<>(queryParamsBO.getPageNum(), queryParamsBO.getPageSize());

        // 处理排序信息，将status字段映射为status_sort_order
        List<OrderItem> orderItems = processStatusSortField(doPage.orders());
        voPage.addOrder(orderItems);

        return pamsAppSummaryInfoMapper.selectPageWithRelation(voPage, queryParamsBO);
    }

    /**
     * 处理状态字段排序，将status映射为status_sort_order
     * SQL中已定义status_sort_order字段：start=1, maintain=2, stop=3
     */
    private List<OrderItem> processStatusSortField(List<OrderItem> originalOrders) {
        List<OrderItem> processedOrders = new ArrayList<>();

        for (OrderItem order : originalOrders) {
            String column = order.getColumn();
            boolean isAsc = order.isAsc();

            if ("status".equals(column)) {
                // 状态字段使用SQL中定义的status_sort_order字段排序
                if (isAsc) {
                    processedOrders.add(OrderItem.asc("status_sort_order"));
                } else {
                    processedOrders.add(OrderItem.desc("status_sort_order"));
                }
            } else {
                // 其他字段保持原有排序
                processedOrders.add(order);
            }
        }

        // 如果没有指定排序，默认按创建时间倒序
        if (processedOrders.isEmpty()) {
            processedOrders.add(OrderItem.desc("create_time"));
        }

        return processedOrders;
    }

    @Override
    public List<DropdownOptionVO> getAppDropdownOptions() {
        List<PamsAppSummaryInfoDO> appList = pamsAppSummaryInfoMapper.selectList(
            new LambdaQueryWrapper<PamsAppSummaryInfoDO>()
                .eq(PamsAppSummaryInfoDO::getDeleted, false)
                .orderByAsc(PamsAppSummaryInfoDO::getAppName)
        );

        return appList.stream()
            .map(app -> DropdownOptionVO.builder()
                .value(app.getId())
                .label(app.getAppName())
                .build())
            .collect(Collectors.toList());
    }

}
