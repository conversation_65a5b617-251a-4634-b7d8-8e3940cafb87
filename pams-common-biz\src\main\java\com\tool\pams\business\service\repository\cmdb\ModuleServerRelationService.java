package com.tool.pams.business.service.repository.cmdb;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tool.pams.repository.domain.cmdb.bo.ModuleServerRelationQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.ModuleServerRelationDO;

/**
 * <p>
 * 模块服务器关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface ModuleServerRelationService extends IService<ModuleServerRelationDO> {

    /**
     * 分页查询
     *
     * @param params 查询参数
     * @return 分页结果
     */
    IPage<ModuleServerRelationDO> page(ModuleServerRelationQueryParamsBO params);

    /**
     * 根据应用ID查询模块服务器关系
     *
     * @param appId 应用ID
     * @return 模块服务器关系列表
     */
    java.util.List<ModuleServerRelationDO> getByAppId(Long appId);

    /**
     * 根据子应用ID查询模块服务器关系
     *
     * @param subAppId 子应用ID(模块ID)
     * @return 模块服务器关系列表
     */
    java.util.List<ModuleServerRelationDO> getBySubAppId(Long subAppId);

}
