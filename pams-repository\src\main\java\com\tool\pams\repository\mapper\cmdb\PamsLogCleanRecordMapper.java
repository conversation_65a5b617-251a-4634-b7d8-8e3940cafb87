package com.tool.pams.repository.mapper.cmdb;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.tool.pams.repository.domain.cmdb.bo.PamsLogCleanRecordQueryParamsBO;
import com.tool.pams.repository.domain.cmdb.db.PamsLogCleanRecordDO;
import com.tool.pams.repository.domain.cmdb.vo.PamsLogCleanRecordPageVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 应用日志清理记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09
 */
@Mapper
public interface PamsLogCleanRecordMapper extends BaseMapper<PamsLogCleanRecordDO> {

    /**
     * 分页查询
     *
     * @param pageInfo
     * @param params
     * @return
     */
    IPage<PamsLogCleanRecordPageVO> page(Page<PamsLogCleanRecordDO> pageInfo, @Param("params") PamsLogCleanRecordQueryParamsBO params);
}
