<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tool.pams.repository.mapper.cmdb.ModuleDependencyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO">
        <id column="id" property="id" />
        <result column="app_id" property="appId" />
        <result column="sub_app_id" property="subAppId" />
        <result column="group_id" property="groupId" />
        <result column="artifact_id" property="artifactId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="creator" property="creator" />
        <result column="updater" property="updater" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_id, sub_app_id, group_id, artifact_id, version, create_time, update_time, creator, updater, deleted
    </sql>

    <!-- 批量插入或更新 -->
    <insert id="insertOrUpdateBatch" parameterType="java.util.List">
        INSERT INTO t_module_dependency (
            id, app_id, sub_app_id, group_id, artifact_id, version, create_time, update_time, creator, updater, deleted
        ) VALUES
        <foreach collection="records" item="record" separator=",">
            (
                #{record.id}, #{record.appId}, #{record.subAppId}, #{record.groupId}, #{record.artifactId},
                #{record.version}, #{record.createTime}, #{record.updateTime},
                #{record.creator}, #{record.updater}, #{record.deleted}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
            app_id = VALUES(app_id),
            sub_app_id = VALUES(sub_app_id),
            group_id = VALUES(group_id),
            artifact_id = VALUES(artifact_id),
            version = VALUES(version),
            update_time = VALUES(update_time),
            updater = VALUES(updater)
    </insert>

    <!-- 分页查询 -->
    <select id="page" resultType="com.tool.pams.repository.domain.cmdb.db.ModuleDependencyDO">
        SELECT
            <include refid="Base_Column_List"/>
        FROM t_module_dependency
        WHERE deleted = 0
        <if test="params.appId != null">
            AND app_id = #{params.appId}
        </if>
        <if test="params.subAppId != null">
            AND sub_app_id = #{params.subAppId}
        </if>
        <if test="params.groupId != null and params.groupId != ''">
            AND group_id LIKE CONCAT('%', #{params.groupId}, '%')
        </if>
        <if test="params.artifactId != null and params.artifactId != ''">
            AND artifact_id LIKE CONCAT('%', #{params.artifactId}, '%')
        </if>
        <if test="params.version != null and params.version != ''">
            AND version LIKE CONCAT('%', #{params.version}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

</mapper>
