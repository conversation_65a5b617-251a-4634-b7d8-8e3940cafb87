package com.tool.pams.repository.domain.cmdb.db;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <p>
 * 新业务系统资源信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-09-09 09:34:16
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_pams_business_system_info")
@Schema(name = "PamsBusinessSystemInfoDO对象", description = "新业务系统资源信息表")
public class PamsBusinessSystemInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主键")
    @TableId("`id`")
    private Long id;

    @Schema(description = "机构ID")
    @TableField("`organization_id`")
    private Long organizationId;

    @Schema(description = "系统编码")
    @TableField("`system_no`")
    private String systemNo;

    @Schema(description = "系统名称")
    @TableField("`system_name`")
    private String systemName;

    @Schema(description = "系统代号")
    @TableField("`system_code`")
    private String systemCode;

    @Schema(description = "系统类别")
    @TableField("`system_type`")
    private String systemType;

    @Schema(description = "账号ID")
    @TableField("`account_id`")
    private String accountId;

    @Schema(description = "负责人")
    @TableField("`director`")
    private String director;

    @Schema(description = "创建时间")
    @TableField(value = "`create_time`", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @TableField(value = "`update_time`", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    @TableField(value = "`creator`", fill = FieldFill.INSERT)
    private String creator;

    @Schema(description = "更新人")
    @TableField(value = "`updater`", fill = FieldFill.INSERT_UPDATE)
    private String updater;

    @Schema(description = "是否删除 0未删除 1已删除")
    @TableField(value = "`deleted`", fill = FieldFill.INSERT)
    @TableLogic
    private Boolean deleted;


}
